using UnityEngine;

namespace GameWish.Game
{
    /// <summary>
    /// 割草状态检测示例脚本
    /// 演示如何使用CS_MowGrass的割草状态跟踪功能
    /// </summary>
    public class CS_MowingStateExample : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private CS_MowGrass m_GrassScript;
        [SerializeField] private Transform m_Player;

        [Header("Mowing Settings")]
        [SerializeField] private float m_MowRadius = 2f;
        [SerializeField] private KeyCode m_MowKey = KeyCode.Space;

        [Header("State Display")]
        [SerializeField] private bool m_ShowMowingState = true;
        [SerializeField] private bool m_LogMowingEvents = true;

        // UI相关
        private string m_CurrentStateText = "";
        private Color m_StateColor = Color.white;

        private void Start()
        {
            if (m_GrassScript == null)
            {
                m_GrassScript = GetComponent<CS_MowGrass>();
            }

            // 订阅割草事件
            if (m_GrassScript != null && m_LogMowingEvents)
            {
                m_GrassScript.OnMowingStarted += OnMowingStarted;
                m_GrassScript.OnMowingEnded += OnMowingEnded;
                m_GrassScript.OnGrassMowed += OnGrassMowed;
            }

            UpdateStateDisplay();
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            if (m_GrassScript != null)
            {
                m_GrassScript.OnMowingStarted -= OnMowingStarted;
                m_GrassScript.OnMowingEnded -= OnMowingEnded;
                m_GrassScript.OnGrassMowed -= OnGrassMowed;
            }
        }

        private void Update()
        {
            HandleInput();
            UpdateStateDisplay();
        }

        private void HandleInput()
        {
            if (Input.GetKeyDown(m_MowKey))
            {
                TryMowGrass();
            }
        }

        /// <summary>
        /// 尝试割草
        /// </summary>
        private void TryMowGrass()
        {
            if (m_Player == null || m_GrassScript == null) return;

            Vector3 playerPos = m_Player.position;

            // 检查是否可以在该位置割草
            if (m_GrassScript.CanMowAtPosition(playerPos, m_MowRadius))
            {
                m_GrassScript.MakeGrassDisappearInArea(playerPos, m_MowRadius);
                Debug.Log($"Mowing at position: {playerPos}");
            }
            else
            {
                if (m_GrassScript.IsInMowingCooldown())
                {
                    Debug.Log("Cannot mow: still in cooldown");
                }
                else
                {
                    Debug.Log("Cannot mow: no grass in area");
                }
            }
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStateDisplay()
        {
            if (m_GrassScript == null) return;

            if (m_GrassScript.IsMowing())
            {
                m_CurrentStateText = "MOWING";
                m_StateColor = Color.red;
            }
            else if (m_GrassScript.IsInMowingCooldown())
            {
                float cooldownRemaining = m_GrassScript.GetTimeSinceLastMow();
                m_CurrentStateText = $"COOLDOWN ({cooldownRemaining:F1}s)";
                m_StateColor = Color.yellow;
            }
            else
            {
                m_CurrentStateText = "READY";
                m_StateColor = Color.green;
            }
        }

        #region 事件处理

        private void OnMowingStarted()
        {
            if (m_LogMowingEvents)
            {
                Debug.Log("🌱 Mowing started!");
            }
        }

        private void OnMowingEnded()
        {
            if (m_LogMowingEvents)
            {
                Debug.Log("✅ Mowing ended!");
            }
        }

        private void OnGrassMowed(Vector3 position, int grassMowed, int remainingGrass)
        {
            if (m_LogMowingEvents)
            {
                Debug.Log($"🔥 Grass mowed at {position}: {grassMowed} grass removed, {remainingGrass} remaining");
            }
        }

        #endregion

        /// <summary>
        /// 显示割草状态UI
        /// </summary>
        private void OnGUI()
        {
            if (!m_ShowMowingState || m_GrassScript == null) return;

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            
            // 状态显示
            GUI.color = m_StateColor;
            GUILayout.Label($"Mowing State: {m_CurrentStateText}", GUI.skin.box);
            GUI.color = Color.white;

            // 详细信息
            GUILayout.Space(10);
            GUILayout.Label($"Is Mowing: {m_GrassScript.IsMowing()}");
            GUILayout.Label($"In Cooldown: {m_GrassScript.IsInMowingCooldown()}");
            GUILayout.Label($"Time Since Last Mow: {m_GrassScript.GetTimeSinceLastMow():F2}s");
            
            Vector3 lastMowPos = m_GrassScript.GetLastMowPosition();
            GUILayout.Label($"Last Mow Position: ({lastMowPos.x:F1}, {lastMowPos.y:F1}, {lastMowPos.z:F1})");

            // 草地统计
            GUILayout.Space(10);
            GUILayout.Label($"Total Grass: {m_GrassScript.GetTotalGrassCount()}");
            GUILayout.Label($"Remaining: {m_GrassScript.GetRemainingGrassCount()}");
            GUILayout.Label($"Disappeared Ratio: {m_GrassScript.GetDisappearedGrassRatio():P1}");

            // 控制说明
            GUILayout.Space(10);
            GUILayout.Label($"Press {m_MowKey} to mow grass");

            GUILayout.EndArea();
        }

        /// <summary>
        /// 在Scene视图中显示割草范围和状态
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (m_Player != null)
            {
                // 显示割草范围
                if (m_GrassScript != null && m_GrassScript.CanMowAtPosition(m_Player.position, m_MowRadius))
                {
                    Gizmos.color = Color.green;
                }
                else
                {
                    Gizmos.color = Color.red;
                }
                
                Gizmos.DrawWireSphere(m_Player.position, m_MowRadius);

                // 显示上次割草位置
                if (m_GrassScript != null)
                {
                    Vector3 lastMowPos = m_GrassScript.GetLastMowPosition();
                    if (lastMowPos != Vector3.zero)
                    {
                        Gizmos.color = Color.blue;
                        Gizmos.DrawWireSphere(lastMowPos, 0.5f);
                        Gizmos.DrawLine(m_Player.position, lastMowPos);
                    }
                }
            }
        }
    }
}
