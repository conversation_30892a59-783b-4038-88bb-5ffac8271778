using UnityEngine;

namespace GameWish.Game
{
    /// <summary>
    /// 简化版CS_MowGrass使用示例
    /// 只支持自定义Mesh和自定义范围两种生成模式
    /// </summary>
    public class CS_SimplifiedMowGrassExample : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private CS_MowGrass m_GrassScript;
        [SerializeField] private Transform m_Player;

        [Header("Mowing Settings")]
        [SerializeField] private float m_MowRadius = 2f;
        [SerializeField] private KeyCode m_MowKey = KeyCode.Space;
        [SerializeField] private KeyCode m_RestoreKey = KeyCode.R;

        [Header("Test Settings")]
        [SerializeField] private Mesh m_TestMesh;
        [SerializeField] private Transform m_TestMeshTransform;
        [SerializeField] private KeyCode m_ToggleMeshKey = KeyCode.M;
        [SerializeField] private KeyCode m_ToggleOrientationKey = KeyCode.G;

        private bool m_UsingCustomMesh = false;

        private void Start()
        {
            if (m_GrassScript == null)
            {
                m_GrassScript = GetComponent<CS_MowGrass>();
            }

            // 订阅割草事件
            if (m_GrassScript != null)
            {
                m_GrassScript.OnMowingStarted += () => Debug.Log("🌱 开始割草!");
                m_GrassScript.OnMowingEnded += () => Debug.Log("✅ 割草结束!");
                m_GrassScript.OnGrassMowed += (pos, count, remaining) =>
                    Debug.Log($"🔥 在{pos}割掉了{count}根草，剩余{remaining}根");
            }

            LogGrassInfo();
        }

        private void OnDestroy()
        {
            // 取消订阅事件
            if (m_GrassScript != null)
            {
                m_GrassScript.OnMowingStarted -= () => Debug.Log("🌱 开始割草!");
                m_GrassScript.OnMowingEnded -= () => Debug.Log("✅ 割草结束!");
                m_GrassScript.OnGrassMowed -= (pos, count, remaining) =>
                    Debug.Log($"🔥 在{pos}割掉了{count}根草，剩余{remaining}根");
            }
        }

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // 基本割草功能
            if (Input.GetKeyDown(m_MowKey))
            {
                MowGrassAroundPlayer();
            }

            if (Input.GetKeyDown(m_RestoreKey))
            {
                RestoreAllGrass();
            }

            // 切换Mesh模式
            if (Input.GetKeyDown(m_ToggleMeshKey))
            {
                ToggleMeshMode();
            }

            // 切换草地朝向
            if (Input.GetKeyDown(m_ToggleOrientationKey))
            {
                ToggleGrassOrientation();
            }
        }

        /// <summary>
        /// 在玩家周围割草
        /// </summary>
        private void MowGrassAroundPlayer()
        {
            if (m_Player != null && m_GrassScript != null)
            {
                Vector3 playerPos = m_Player.position;

                if (m_GrassScript.CanMowAtPosition(playerPos, m_MowRadius))
                {
                    m_GrassScript.MakeGrassDisappearInArea(playerPos, m_MowRadius);
                }
                else
                {
                    Debug.Log("无法割草：冷却中或该区域没有草");
                }
            }
        }

        /// <summary>
        /// 恢复所有草地
        /// </summary>
        private void RestoreAllGrass()
        {
            if (m_GrassScript != null)
            {
                m_GrassScript.RestoreAllGrass();
                Debug.Log("所有草地已恢复!");
                LogGrassInfo();
            }
        }

        /// <summary>
        /// 切换Mesh模式
        /// </summary>
        private void ToggleMeshMode()
        {
            if (m_GrassScript != null)
            {
                if (m_UsingCustomMesh)
                {
                    // 切换到自定义范围模式
                    m_GrassScript.SetCustomMesh(null, null);
                    m_UsingCustomMesh = false;
                    Debug.Log("切换到自定义范围生成模式");
                }
                else
                {
                    // 切换到自定义Mesh模式
                    if (m_TestMesh != null)
                    {
                        m_GrassScript.SetCustomMesh(m_TestMesh, m_TestMeshTransform);
                        m_UsingCustomMesh = true;
                        Debug.Log($"切换到自定义Mesh生成模式: {m_TestMesh.name}");
                    }
                    else
                    {
                        Debug.LogWarning("没有设置测试Mesh!");
                    }
                }

                LogGrassInfo();
            }
        }

        /// <summary>
        /// 切换草地朝向模式
        /// </summary>
        private void ToggleGrassOrientation()
        {
            if (m_GrassScript != null)
            {
                CS_MowGrass.GrassOrientationMode currentMode = m_GrassScript.GetGrassOrientationMode();
                CS_MowGrass.GrassOrientationMode nextMode;

                switch (currentMode)
                {
                    case CS_MowGrass.GrassOrientationMode.AlwaysUp:
                        nextMode = CS_MowGrass.GrassOrientationMode.FollowSurfaceNormal;
                        break;
                    case CS_MowGrass.GrassOrientationMode.FollowSurfaceNormal:
                        nextMode = CS_MowGrass.GrassOrientationMode.MixedOrientation;
                        break;
                    case CS_MowGrass.GrassOrientationMode.MixedOrientation:
                        nextMode = CS_MowGrass.GrassOrientationMode.AlwaysUp;
                        break;
                    default:
                        nextMode = CS_MowGrass.GrassOrientationMode.AlwaysUp;
                        break;
                }

                m_GrassScript.SetGrassOrientationMode(nextMode);
                Debug.Log($"草地朝向改为: {nextMode}");
            }
        }

        /// <summary>
        /// 输出草地信息
        /// </summary>
        private void LogGrassInfo()
        {
            if (m_GrassScript != null)
            {
                int total = m_GrassScript.GetTotalGrassCount();
                int remaining = m_GrassScript.GetRemainingGrassCount();
                float ratio = m_GrassScript.GetDisappearedGrassRatio();
                Vector2 area = m_GrassScript.GetCurrentGrassArea();
                Vector3 center = m_GrassScript.GetCurrentAreaCenter();

                Debug.Log($"=== 草地信息 ===");
                Debug.Log($"总数: {total}, 剩余: {remaining}, 消失比例: {ratio:P1}");
                Debug.Log($"生成区域: {area}, 中心: {center}");
                Debug.Log($"生成模式: {(m_UsingCustomMesh ? "自定义Mesh" : "自定义范围")}");
                Debug.Log($"===============");
            }
        }

        /// <summary>
        /// 显示控制UI
        /// </summary>
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 350, 250));
            GUILayout.Label("简化版草地控制:", GUI.skin.box);

            GUILayout.Label($"Press {m_MowKey} - 割草");
            GUILayout.Label($"Press {m_RestoreKey} - 恢复草地");
            GUILayout.Label($"Press {m_ToggleMeshKey} - 切换生成模式");
            GUILayout.Label($"Press {m_ToggleOrientationKey} - 切换草地朝向");

            if (m_GrassScript != null)
            {
                GUILayout.Space(10);

                // 状态显示
                if (m_GrassScript.IsMowing())
                {
                    GUI.color = Color.red;
                    GUILayout.Label("状态: 割草中", GUI.skin.box);
                }
                else if (m_GrassScript.IsInMowingCooldown())
                {
                    GUI.color = Color.yellow;
                    GUILayout.Label("状态: 冷却中", GUI.skin.box);
                }
                else
                {
                    GUI.color = Color.green;
                    GUILayout.Label("状态: 准备就绪", GUI.skin.box);
                }
                GUI.color = Color.white;

                // 草地信息
                GUILayout.Space(5);
                GUILayout.Label($"总草数: {m_GrassScript.GetTotalGrassCount()}");
                GUILayout.Label($"剩余: {m_GrassScript.GetRemainingGrassCount()}");
                GUILayout.Label($"消失比例: {m_GrassScript.GetDisappearedGrassRatio():P1}");

                string mode = m_UsingCustomMesh ? "自定义Mesh" : "自定义范围";
                GUILayout.Label($"生成模式: {mode}");

                CS_MowGrass.GrassOrientationMode orientation = m_GrassScript.GetGrassOrientationMode();
                GUILayout.Label($"草地朝向: {orientation}");
            }

            GUILayout.EndArea();
        }

        /// <summary>
        /// 在Scene视图中显示割草范围
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (m_Player != null && m_GrassScript != null)
            {
                // 显示割草范围
                if (m_GrassScript.CanMowAtPosition(m_Player.position, m_MowRadius))
                {
                    Gizmos.color = Color.green;
                }
                else
                {
                    Gizmos.color = Color.red;
                }

                Gizmos.DrawWireSphere(m_Player.position, m_MowRadius);

                // 显示上次割草位置
                Vector3 lastMowPos = m_GrassScript.GetLastMowPosition();
                if (lastMowPos != Vector3.zero)
                {
                    Gizmos.color = Color.blue;
                    Gizmos.DrawWireSphere(lastMowPos, 0.5f);
                }
            }
        }
    }
}
