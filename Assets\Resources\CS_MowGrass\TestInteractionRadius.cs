using UnityEngine;

/// <summary>
/// 测试草地交互半径效果的脚本
/// 用于验证修改后的shader是否正确限制了交互范围
/// </summary>
public class TestInteractionRadius : MonoBehaviour
{
    [Header("交互设置")]
    [SerializeField] private float interactionRadius = 5f;
    [SerializeField] private Material grassMaterial;
    
    [Header("调试显示")]
    [SerializeField] private bool showGizmos = true;
    [SerializeField] private Color gizmoColor = Color.yellow;
    
    private void Start()
    {
        // 如果没有指定材质，尝试自动查找
        if (grassMaterial == null)
        {
            // 查找场景中的草地材质
            var grassScript = FindObjectOfType<GameWish.Game.CS_MowGrass>();
            if (grassScript != null)
            {
                // 这里需要根据实际的材质获取方式来调整
                Debug.Log("找到草地脚本，请手动设置材质引用");
            }
        }
    }
    
    private void FixedUpdate()
    {
        // 更新角色位置到shader
        Shader.SetGlobalVector("_PlayerForward", transform.position);
        
        // 更新交互半径到材质
        if (grassMaterial != null)
        {
            grassMaterial.SetFloat("_PlayerInteractionRadius", interactionRadius);
        }
        else
        {
            // 如果没有材质引用，使用全局设置
            Shader.SetGlobalFloat("_PlayerInteractionRadius", interactionRadius);
        }
    }
    
    private void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        // 绘制交互范围
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireSphere(transform.position, interactionRadius);
        
        // 绘制角色位置
        Gizmos.color = Color.red;
        Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!showGizmos) return;
        
        // 选中时显示更详细的信息
        Gizmos.color = Color.green;
        Gizmos.DrawSphere(transform.position, 0.2f);
        
        // 显示交互范围的填充圆
        Gizmos.color = new Color(gizmoColor.r, gizmoColor.g, gizmoColor.b, 0.1f);
        Gizmos.DrawSphere(transform.position, interactionRadius);
    }
    
    /// <summary>
    /// 在Inspector中调整半径时实时更新
    /// </summary>
    private void OnValidate()
    {
        interactionRadius = Mathf.Max(0.1f, interactionRadius);
    }
}
