# 草地交互范围修改说明

## 问题描述
原始的草地交互效果影响范围过大，所有草都会受到角色位置的影响，缺乏距离限制。

## 修改内容

### 1. 新增参数
在shader中添加了新的参数：
- `_PlayerInteractionRadius`: 控制角色交互的影响半径

### 2. 修改的文件
- `CS_MowGrass_URP.shader` - URP版本shader
- `CS_MowGrass.shader` - 标准渲染管线版本shader  
- `URP_MowGrass_CS_MowGrass.mat` - URP材质文件
- `CS_MowGrass.mat` - 标准材质文件

### 3. 核心修改逻辑
在`GetWaveDelta`函数中添加了距离检查：

```hlsl
// 计算草与角色的距离
float3 toPlayer = worldPos - _PlayerForward;
float distanceToPlayer = length(toPlayer);

// 如果超出交互范围，返回零偏移
if (distanceToPlayer > _PlayerInteractionRadius)
{
    return float3(0, 0, 0);
}

// 计算距离衰减因子 (1.0 在中心，0.0 在边缘)
float distanceFactor = 1.0 - saturate(distanceToPlayer / _PlayerInteractionRadius);
distanceFactor = smoothstep(0.0, 1.0, distanceFactor); // 平滑衰减
```

### 4. 效果特点
- **距离限制**: 只有在`_PlayerInteractionRadius`范围内的草才会受到影响
- **平滑衰减**: 使用`smoothstep`函数实现从中心到边缘的平滑过渡
- **性能优化**: 超出范围的草直接返回零偏移，减少计算量

## 使用方法

### 1. 材质设置
在材质的Inspector面板中调整`Player Interaction Radius`参数：
- 默认值：5.0
- 建议范围：1.0 - 10.0

### 2. 脚本控制
使用`TestInteractionRadius.cs`脚本可以：
- 实时调整交互半径
- 可视化显示交互范围
- 动态更新shader参数

### 3. 全局设置
也可以通过代码全局设置：
```csharp
Shader.SetGlobalFloat("_PlayerInteractionRadius", 5.0f);
```

## 测试建议
1. 将`TestInteractionRadius.cs`脚本添加到角色对象上
2. 在Scene视图中观察黄色线框圆圈（交互范围）
3. 调整`Interaction Radius`参数观察效果变化
4. 确认只有范围内的草会产生交互效果

## 注意事项
- 修改后需要重新编译shader
- 如果使用自定义材质，需要手动添加`_PlayerInteractionRadius`参数
- 建议根据场景大小和草地密度调整合适的交互半径值
