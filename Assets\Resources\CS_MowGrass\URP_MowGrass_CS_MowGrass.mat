%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: URP_MowGrass_CS_MowGrass
  m_Shader: {fileID: 4800000, guid: 9834cd0e372b3214ab3e8ccb6afa3d8a, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _GrassPlayerWave: 2
    - _NoiseIntensity: 0
    - _NoiseSpeed: 0
    - _PlayerInteractionRadius: 5
    - _WaveA: 1
    - _WaveL: 1
    - _WaveS: 1
    m_Colors:
    - _BottomColor: {r: 1, g: 1, b: 1, a: 1}
    - _TopColor: {r: 1, g: 1, b: 1, a: 1}
    - _WaveSinColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
